import { softDeleteMiddleware } from './prisma.middleware';

describe('실제 프로덕션 코드 시나리오 테스트', () => {
  let middleware: any;
  let mockNext: jest.Mock;

  beforeEach(() => {
    middleware = softDeleteMiddleware();
    mockNext = jest.fn().mockResolvedValue({});
  });

  describe('booth.creator.service.ts 실제 코드 시나리오', () => {
    it('현재 코드: 단순한 boothUuid 조건만 있는 경우', async () => {
      // 현재 실제 코드와 동일한 구조
      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          where: { name: { contains: 'test' } },
          include: {
            goodsCharacterPivot: { include: { character: true } },
            goodsCategory: { select: { name: true } },
            goodsBoothPivot: {
              where: { boothUuid: 'booth-uuid-456' },
              select: {
                quantity: true,
                prepareCount: true,
                advanceCount: true,
                sellingMethods: true,
              },
            },
          },
          orderBy: { goodsUuid: 'desc' },
        },
      };

      await middleware(params, mockNext);

      // 기존 구조는 그대로 유지되어야 함
      expect(params.args.include.goodsBoothPivot.where).toEqual({
        boothUuid: 'booth-uuid-456',
      });
      expect(params.args.where.deletedAt).toBeNull();
    });

    it('복합 키가 추가된 경우: boothUuid + goodsUuid_boothUuid 조건', async () => {
      // 실제 코드에 복합 키 조건이 추가되는 경우
      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          where: { name: { contains: 'test' } },
          include: {
            goodsCharacterPivot: { include: { character: true } },
            goodsCategory: { select: { name: true } },
            goodsBoothPivot: {
              where: {
                boothUuid: 'booth-uuid-456',
                // 새로 추가될 수 있는 복합 키 조건
                goodsUuid_boothUuid: {
                  goodsUuid: 'goods-uuid-123',
                  boothUuid: 'booth-uuid-456',
                },
              },
              select: {
                quantity: true,
                prepareCount: true,
                advanceCount: true,
                sellingMethods: true,
              },
            },
          },
          orderBy: { goodsUuid: 'desc' },
        },
      };

      await middleware(params, mockNext);

      // 복합 키가 개별 필드로 변환되고 기존 조건과 병합되어야 함
      expect(params.args.include.goodsBoothPivot.where).toEqual({
        boothUuid: 'booth-uuid-456', // 기존 조건 유지
        goodsUuid: 'goods-uuid-123', // 복합 키에서 추출된 필드
      });

      // 복합 키 원본은 제거되어야 함
      expect(
        params.args.include.goodsBoothPivot.where.goodsUuid_boothUuid,
      ).toBeUndefined();

      // soft delete 필터도 추가되어야 함
      expect(params.args.where.deletedAt).toBeNull();
    });

    it('복합 키만 있는 경우', async () => {
      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          where: { name: { contains: 'test' } },
          include: {
            goodsBoothPivot: {
              where: {
                goodsUuid_boothUuid: {
                  goodsUuid: 'goods-uuid-123',
                  boothUuid: 'booth-uuid-456',
                },
              },
              select: {
                quantity: true,
                prepareCount: true,
                advanceCount: true,
                sellingMethods: true,
              },
            },
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.args.include.goodsBoothPivot.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
      });
      expect(
        params.args.include.goodsBoothPivot.where.goodsUuid_boothUuid,
      ).toBeUndefined();
    });

    it('트랜잭션 내부에서도 정상 작동해야 함', async () => {
      // $transaction 내부의 쿼리들도 middleware를 거쳐야 함
      const findManyParams: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          where: { name: { contains: 'test' } },
          include: {
            goodsBoothPivot: {
              where: {
                goodsUuid_boothUuid: {
                  goodsUuid: 'goods-uuid-123',
                  boothUuid: 'booth-uuid-456',
                },
              },
              select: { quantity: true },
            },
          },
        },
      };

      const countParams: any = {
        model: 'Goods',
        action: 'count',
        args: {
          where: { name: { contains: 'test' } },
        },
      };

      await middleware(findManyParams, mockNext);
      await middleware(countParams, mockNext);

      // findMany의 중첩된 복합 키 변환 확인
      expect(findManyParams.args.include.goodsBoothPivot.where).toEqual({
        goodsUuid: 'goods-uuid-123',
        boothUuid: 'booth-uuid-456',
      });

      // count의 soft delete 필터 확인
      expect(countParams.args.where.deletedAt).toBeNull();
    });

    it('다양한 조건과 복합 키가 함께 있는 복잡한 경우', async () => {
      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          where: {
            name: { contains: 'test' },
            price: { gte: 1000 },
          },
          include: {
            goodsBoothPivot: {
              where: {
                boothUuid: 'booth-uuid-456',
                quantity: { gt: 0 },
                goodsUuid_boothUuid: {
                  goodsUuid: 'goods-uuid-123',
                  boothUuid: 'booth-uuid-456',
                },
                prepareCount: { gte: 1 },
              },
              select: {
                quantity: true,
                prepareCount: true,
                advanceCount: true,
                sellingMethods: true,
              },
            },
          },
        },
      };

      await middleware(params, mockNext);

      expect(params.args.include.goodsBoothPivot.where).toEqual({
        boothUuid: 'booth-uuid-456',
        quantity: { gt: 0 },
        goodsUuid: 'goods-uuid-123',
        prepareCount: { gte: 1 },
      });
      expect(
        params.args.include.goodsBoothPivot.where.goodsUuid_boothUuid,
      ).toBeUndefined();
    });
  });

  describe('성능 및 안정성 테스트', () => {
    it('복잡한 중첩 구조에서 성능 테스트', async () => {
      const startTime = Date.now();

      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          include: {
            goodsBoothPivot: {
              where: {
                goodsUuid_boothUuid: { goodsUuid: 'g1', boothUuid: 'b1' },
              },
              include: {
                booth: {
                  select: {
                    goodsBoothPivot: {
                      where: {
                        goodsUuid_boothUuid: {
                          goodsUuid: 'g2',
                          boothUuid: 'b2',
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      };

      await middleware(params, mockNext);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // 50ms 이내에 완료되어야 함
      expect(executionTime).toBeLessThan(50);

      // 첫 번째 레벨 변환 확인
      expect(params.args.include.goodsBoothPivot.where).toEqual({
        goodsUuid: 'g1',
        boothUuid: 'b1',
      });

      // 두 번째 레벨 변환 확인 (구조가 존재하는 경우에만)
      if (
        params.args.include.goodsBoothPivot.include?.booth?.select
          ?.goodsBoothPivot?.where
      ) {
        expect(
          params.args.include.goodsBoothPivot.include.booth.select
            .goodsBoothPivot.where,
        ).toEqual({
          goodsUuid: 'g2',
          boothUuid: 'b2',
        });
      }
    });

    it('빈 객체나 null 값에 대한 안정성 테스트', async () => {
      const params: any = {
        model: 'Goods',
        action: 'findMany',
        args: {
          include: {
            goodsBoothPivot: {
              where: null,
            },
          },
        },
      };

      // 에러 없이 처리되어야 함
      await expect(middleware(params, mockNext)).resolves.not.toThrow();
    });
  });
});

/**
 * 실제 프로덕션 코드에서 중첩된 복합 키 변환이 어떻게 작동하는지 보여주는 데모
 * 
 * 이 파일은 선택된 코드(booth.creator.service.ts)와 같은 실제 시나리오에서
 * 복합 키 조건이 추가될 때 middleware가 어떻게 처리하는지 보여줍니다.
 */

import { softDeleteMiddleware } from './prisma.middleware';

export async function demonstrateProductionScenarios() {
  const middleware = softDeleteMiddleware();
  const mockNext = async () => ({ result: 'mocked' });

  console.log('🔧 실제 프로덕션 코드 시나리오에서 중첩된 복합 키 변환 데모\n');

  // 시나리오 1: 현재 코드 (단순한 boothUuid 조건)
  console.log('📋 시나리오 1: 현재 실제 코드와 동일한 구조');
  const currentCode: any = {
    model: 'Goods',
    action: 'findMany',
    args: {
      where: { name: { contains: 'test' } },
      include: {
        goodsCharacterPivot: { include: { character: true } },
        goodsCategory: { select: { name: true } },
        goodsBoothPivot: {
          where: { boothUuid: 'booth-uuid-456' },
          select: {
            quantity: true,
            prepareCount: true,
            advanceCount: true,
            sellingMethods: true,
          },
        },
      },
      orderBy: { goodsUuid: 'desc' },
    },
  };

  console.log('현재 코드의 goodsBoothPivot where 조건:');
  console.log(JSON.stringify(currentCode.args.include.goodsBoothPivot.where, null, 2));

  await middleware(currentCode, mockNext);

  console.log('\nMiddleware 처리 후:');
  console.log(JSON.stringify(currentCode.args.include.goodsBoothPivot.where, null, 2));
  console.log('✅ 기존 구조는 그대로 유지됨\n');

  // 시나리오 2: 복합 키가 추가된 경우
  console.log('📋 시나리오 2: 복합 키 조건이 추가된 경우');
  const withCompositeKey: any = {
    model: 'Goods',
    action: 'findMany',
    args: {
      where: { name: { contains: 'test' } },
      include: {
        goodsCharacterPivot: { include: { character: true } },
        goodsCategory: { select: { name: true } },
        goodsBoothPivot: {
          where: {
            // 기존 조건
            boothUuid: 'booth-uuid-456',
            // 새로 추가된 복합 키 조건
            goodsUuid_boothUuid: {
              goodsUuid: 'goods-uuid-123',
              boothUuid: 'booth-uuid-456',
            },
          },
          select: {
            quantity: true,
            prepareCount: true,
            advanceCount: true,
            sellingMethods: true,
          },
        },
      },
      orderBy: { goodsUuid: 'desc' },
    },
  };

  console.log('복합 키가 추가된 goodsBoothPivot where 조건:');
  console.log(JSON.stringify(withCompositeKey.args.include.goodsBoothPivot.where, null, 2));

  await middleware(withCompositeKey, mockNext);

  console.log('\nMiddleware 처리 후:');
  console.log(JSON.stringify(withCompositeKey.args.include.goodsBoothPivot.where, null, 2));
  console.log('✅ 복합 키가 개별 필드로 변환되고 기존 조건과 병합됨\n');

  // 시나리오 3: 복잡한 중첩 구조
  console.log('📋 시나리오 3: 복잡한 중첩 구조에서의 복합 키 변환');
  const complexNested: any = {
    model: 'Goods',
    action: 'findMany',
    args: {
      where: { name: { contains: 'test' } },
      include: {
        goodsBoothPivot: {
          where: {
            goodsUuid_boothUuid: {
              goodsUuid: 'level1-goods',
              boothUuid: 'level1-booth',
            },
          },
          include: {
            booth: {
              select: {
                name: true,
                goodsBoothPivot: {
                  where: {
                    goodsUuid_boothUuid: {
                      goodsUuid: 'level2-goods',
                      boothUuid: 'level2-booth',
                    },
                  },
                  select: { quantity: true },
                },
              },
            },
          },
        },
      },
    },
  };

  console.log('중첩 구조의 복합 키 조건들:');
  console.log('Level 1:', JSON.stringify(complexNested.args.include.goodsBoothPivot.where, null, 2));
  console.log('Level 2:', JSON.stringify(complexNested.args.include.goodsBoothPivot.include.booth.select.goodsBoothPivot.where, null, 2));

  await middleware(complexNested, mockNext);

  console.log('\nMiddleware 처리 후:');
  console.log('Level 1:', JSON.stringify(complexNested.args.include.goodsBoothPivot.where, null, 2));
  console.log('Level 2:', JSON.stringify(complexNested.args.include.goodsBoothPivot.include.booth.select.goodsBoothPivot.where, null, 2));
  console.log('✅ 모든 중첩 레벨에서 복합 키가 올바르게 변환됨\n');

  // 시나리오 4: 성능 확인
  console.log('📋 시나리오 4: 성능 테스트');
  const performanceTest: any = {
    model: 'Goods',
    action: 'findMany',
    args: {
      include: {
        goodsBoothPivot: {
          where: {
            goodsUuid_boothUuid: { goodsUuid: 'perf1', boothUuid: 'perf1' },
          },
          include: {
            booth: {
              select: {
                goodsBoothPivot: {
                  where: {
                    goodsUuid_boothUuid: { goodsUuid: 'perf2', boothUuid: 'perf2' },
                  },
                },
              },
            },
          },
        },
      },
    },
  };

  const startTime = Date.now();
  await middleware(performanceTest, mockNext);
  const endTime = Date.now();

  console.log(`실행 시간: ${endTime - startTime}ms`);
  console.log('✅ 복잡한 중첩 구조도 빠르게 처리됨\n');

  console.log('🎉 결론:');
  console.log('1. 기존 코드는 변경 없이 그대로 작동');
  console.log('2. 복합 키 조건이 추가되면 자동으로 개별 필드로 변환');
  console.log('3. 중첩된 구조에서도 재귀적으로 처리');
  console.log('4. 성능에 미치는 영향 최소화');
  console.log('5. 실제 프로덕션 환경에서 안정적으로 사용 가능');
}

// 데모 실행 (필요시 주석 해제)
// demonstrateProductionScenarios().catch(console.error);

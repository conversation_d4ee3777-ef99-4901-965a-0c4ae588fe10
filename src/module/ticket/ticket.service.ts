import { ONE_YEAR_TO_MS } from '@common/lib/const';
import { PaymentMethodType } from '@common/types/payment.types';
import { TICKET_PRICE, TicketDuration } from '@common/types/ticket.types';
import { PaypleService } from '@module/payple/payple.service';
import { ConflictException, Injectable } from '@nestjs/common';
import type { Prisma } from '@prisma/client';
import { PrismaService } from '@provider/database/prisma.service';
import { v7 as uuidv7 } from 'uuid';
import { TicketCreateDto } from './dto/ticket.create.dto';
import { TicketPgDataDto } from './dto/ticket.payment.dto';

@Injectable()
export class TicketService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly paypleService: PaypleService,
  ) {}

  getProductTickets() {
    return Object.entries(TICKET_PRICE).map(([days, price]) => ({
      days: parseInt(days, 10),
      price,
    }));
  }

  async getTicketsByUserUuid(userUuid: string) {
    return this.prismaService.ticket.findMany({
      where: { userUuid },
    });
  }

  async buyTicketPrepare(userUuid: string, dto: TicketCreateDto) {
    const orderId = uuidv7();
    if (dto.isTest) {
      await this.prismaService.ticket.create({
        data: {
          ticketUuid: orderId,
          userUuid,
          usableDay: dto.usableDay,
          expiredAt: new Date(Date.now() + 5 * ONE_YEAR_TO_MS),
        },
      });
      // 테스트용
      return {
        isTest: true,
        clientKey: 'testClientKey',
        PCD_PAY_TYPE: 'testPayType',
        PCD_PAY_WORK: 'testPayWork',
        PCD_ORDER_ID: orderId,
        PCD_TID: 'testTid',
        PCD_AUTH_NO: 'testAuthNo',
        PCD_AUTH_DATE: new Date().toISOString(),
      } as const;
    }
    await this.prismaService.prepareTicket.create({
      data: {
        ticketUuid: orderId,
        userUuid,
        usableDay: dto.usableDay,
        expiredAt: new Date(Date.now() + 5 * ONE_YEAR_TO_MS),
      },
    });

    return {
      orderUuid: orderId,
      payple: this.paypleService.getPgData({
        payType: PaymentMethodType.CARD,
        goodsTitle: `이용권 ${dto.usableDay}일`,
        totalPrice: TICKET_PRICE[dto.usableDay],
        orderUuid: orderId,
      }),
    };
  }

  async getTicketPreparePgData(dto: TicketPgDataDto) {
    const prepareOrder = await this.prismaService.prepareTicket.findUnique({
      where: {
        ticketUuid: dto.orderUuid,
      },
    });

    if (!prepareOrder) {
      const ticket = await this.prismaService.ticket.findUnique({
        where: {
          ticketUuid: dto.orderUuid,
        },
      });
      if (ticket) {
        throw new ConflictException('이미 결제된 티켓입니다.');
      } else {
        throw new ConflictException('존재하지 않는 티켓입니다.');
      }
    }

    return this.paypleService.getPgData({
      payType: PaymentMethodType.CARD,
      goodsTitle: `이용권 ${prepareOrder.usableDay}일`,
      totalPrice: TICKET_PRICE[prepareOrder.usableDay as TicketDuration],
      orderUuid: prepareOrder.ticketUuid,
    });
  }

  async syncTicket(
    userUuid: string,
    boothUuid: string,
    newParticipatedDates: Date[],
    tx?: Prisma.TransactionClient,
  ) {
    // 1. 유저가 해당 부스의 admin인지 확인
    const boothParticipatedUser = await (
      tx || this.prismaService
    ).boothParticipatedUser.findUnique({
      where: {
        boothUuid_userUuid: {
          boothUuid,
          userUuid,
        },
      },
    });

    if (!boothParticipatedUser?.isAdmin) {
      return;
    }

    // 2. 트랜잭션 내에서 모든 작업 수행
    const syncTicketTransaction = async (prisma: Prisma.TransactionClient) => {
      // 필요한 모든 데이터를 병렬로 조회하여 성능 최적화
      const [booth, existingTicketHistories] = await Promise.all([
        prisma.booth.findUnique({
          where: { boothUuid },
          select: { participatedDates: true },
        }),
        prisma.ticketHistory.findMany({
          where: { boothUuid },
          select: {
            ticketUuid: true,
            usedDate: true,
          },
        }),
      ]);

      if (!booth) {
        return;
      }

      // 기존 participatedDates와 새로운 participatedDates 비교
      const currentDates = booth.participatedDates.map((date) =>
        date.toISOString(),
      );
      const newDates = newParticipatedDates.map((date) => date.toISOString());

      // 날짜가 같으면 return (변경사항 없음)
      if (
        currentDates.length === newDates.length &&
        currentDates.every((date) => newDates.includes(date))
      ) {
        return;
      }

      // 제거/추가 날짜 계산
      const removedDates = currentDates.filter(
        (date) => !newDates.includes(date),
      );
      const addedDates = newDates.filter(
        (date) => !currentDates.includes(date),
      );

      // 배치 작업을 위한 배열들
      const batchOperations: Promise<any>[] = [];

      // 제거된 날짜들에 대한 TicketHistory 삭제 및 티켓 usedDay 복원
      if (removedDates.length > 0) {
        const removedDateObjects = removedDates.map((date) => new Date(date));

        // 삭제할 TicketHistory 필터링
        const ticketHistoriesToDelete = existingTicketHistories.filter(
          (history) =>
            removedDateObjects.some(
              (date) => date.getTime() === history.usedDate.getTime(),
            ),
        );

        // 티켓별 복원할 사용량 계산
        const ticketRestoreMap = new Map<string, number>();
        for (const history of ticketHistoriesToDelete) {
          const currentRestore = ticketRestoreMap.get(history.ticketUuid) || 0;
          ticketRestoreMap.set(history.ticketUuid, currentRestore + 1);
        }

        // 배치로 티켓 usedDay 복원을 위한 업데이트 작업들 준비
        const sortedRestoreEntries = Array.from(
          ticketRestoreMap.entries(),
        ).sort(([a], [b]) => a.localeCompare(b));

        for (const [ticketUuid, restoreDays] of sortedRestoreEntries) {
          batchOperations.push(
            prisma.ticket.update({
              where: { ticketUuid },
              data: { usedDay: { decrement: restoreDays } },
            }),
          );
        }

        // TicketHistory 삭제 작업 추가
        batchOperations.push(
          prisma.ticketHistory.deleteMany({
            where: {
              boothUuid,
              usedDate: { in: removedDateObjects },
            },
          }),
        );
      }

      // 새로 추가된 날짜들에 대한 TicketHistory 생성
      if (addedDates.length > 0) {
        const addedDateObjects = addedDates.map((date) => new Date(date));

        // 해당 사용자의 사용 가능한 티켓 조회 (오래된 순으로)
        const userTickets = await prisma.ticket.findMany({
          where: {
            userUuid,
            expiredAt: { gt: new Date() }, // 만료되지 않은 티켓
            usedDay: { lt: prisma.ticket.fields.usableDay }, // usedDay < usableDay
          },
          orderBy: { ticketUuid: 'asc' }, // 오래된 순
        });

        const ticketHistoryData: Array<{
          ticketUuid: string;
          boothUuid: string;
          usedDate: Date;
        }> = [];

        let remainingDays = addedDateObjects.length;
        let dateIndex = 0;
        const ticketUsageMap = new Map<string, number>();

        // 사용자의 티켓을 순회하며 사용 가능한 티켓 찾기
        for (const ticket of userTickets) {
          if (remainingDays <= 0) {
            break;
          }

          const availableDays = ticket.usableDay - ticket.usedDay;
          const daysToUse = Math.min(availableDays, remainingDays);

          // 해당 티켓으로 사용할 날짜들 할당
          for (let i = 0; i < daysToUse; ++i) {
            ticketHistoryData.push({
              ticketUuid: ticket.ticketUuid,
              boothUuid,
              usedDate: addedDateObjects[dateIndex],
            });
            --remainingDays;
            ++dateIndex;
          }

          // 티켓별 사용량 누적
          const currentUsage = ticketUsageMap.get(ticket.ticketUuid) || 0;
          ticketUsageMap.set(ticket.ticketUuid, currentUsage + daysToUse);
        }

        // 티켓이 부족한지 확인
        if (remainingDays > 0) {
          throw new ConflictException(
            `티켓이 ${remainingDays}일 부족합니다. 추가 티켓을 구매하거나 날짜를 줄여주세요.`,
          );
        }

        // 배치로 티켓 usedDay 업데이트를 위한 작업들 준비
        const sortedUsageEntries = Array.from(ticketUsageMap.entries()).sort(
          ([a], [b]) => a.localeCompare(b),
        );

        for (const [ticketUuid, totalDaysUsed] of sortedUsageEntries) {
          batchOperations.push(
            prisma.ticket.update({
              where: { ticketUuid },
              data: { usedDay: { increment: totalDaysUsed } },
            }),
          );
        }

        // TicketHistory 생성 작업 추가
        if (ticketHistoryData.length > 0) {
          batchOperations.push(
            prisma.ticketHistory.createMany({
              data: ticketHistoryData,
            }),
          );
        }
      }

      // 부스의 participatedDates 업데이트 작업 추가
      batchOperations.push(
        prisma.booth.update({
          where: { boothUuid },
          data: { participatedDates: newParticipatedDates },
        }),
      );

      // 모든 배치 작업을 병렬로 실행
      await Promise.all(batchOperations);
    };

    if (tx) {
      await syncTicketTransaction(tx);
    } else {
      await this.prismaService.$transaction(syncTicketTransaction);
    }
  }
}
